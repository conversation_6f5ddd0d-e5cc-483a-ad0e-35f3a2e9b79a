/**
 * tools/shadcn-add.mjs
 *
 * pnpm-only helper to pull shadcn-style registry items and place files
 * into your monorepo component lib (default: packages/ui).
 *
 * Command
 *   node tools/shadcn-add.mjs add <item...>
 *   # e.g. node tools/shadcn-add.mjs add button input form
 *
 * Behavior
 *   - Resolves registryDependencies recursively.
 *   - Writes files using a type->folder mapping tailored to your repo.
 *   - Installs *all runtime deps as peerDependencies* with pnpm (--save-peer).
 *   - Installs devDependencies as dev deps (-D).
 *   - Prints Tailwind/CSS/env hints (non-destructive).
 *
 * Options (env or flags)
 *   --pkg         Target package dir (default: packages/ui)
 *   --registry    Registry base (default: https://ui.shadcn.com)
 *   --dry-run     Preview without writing/installing
 *
 * ENV overrides: SHADCN_PKG, SHADCN_REGISTRY
 */

/* eslint-disable no-console */
import { promises as fs } from "fs";
import path from "path";
import { spawnSync } from "child_process";
import os from "os";

const DEFAULTS = {
  registry: process.env.SHADCN_REGISTRY || "https://ui.shadcn.com/r/styles/new-york/",
  pkg: process.env.SHADCN_PKG || "packages/ui",
};

// Where to place files per file.type
const TYPE_TARGETS = {
  "registry:ui": "src/components",
  "registry:component": "src/components",
  "registry:hook": "src/hooks", // change to src/hooks if you prefer
  "registry:lib": "src/lib",
  "registry:style": "src/styles",
  "registry:file": "", // trust file.target
  "registry:page": "", // trust file.target
};

// --- CLI ---
function parseArgs(argv) {
  const args = { _: [] };
  for (let i = 2; i < argv.length; i++) {
    const a = argv[i];
    if (a.startsWith("--")) {
      const k = a.slice(2);
      const v = argv[i + 1] && !argv[i + 1].startsWith("--") ? argv[++i] : true;
      args[k] = v;
    } else {
      args._.push(a);
    }
  }
  return args;
}

function assertPnpm() {
  const r = spawnSync("pnpm", ["-v"], { stdio: "ignore" });
  if (r.status !== 0) {
    console.error("This tool requires pnpm to be installed and on PATH.");
    process.exit(1);
  }
}

function isUrl(s) {
  try {
    new URL(s);
    return true;
  } catch {
    return false;
  }
}
function dedupe(a) {
  return Array.from(new Set((a || []).filter(Boolean)));
}
async function ensureDir(p) {
  await fs.mkdir(p, { recursive: true });
}

async function repoRoot(start = process.cwd()) {
  let dir = start;
  while (true) {
    try {
      await fs.access(path.join(dir, "pnpm-workspace.yaml"));
      return dir;
    } catch {}
    try {
      await fs.access(path.join(dir, "package.json"));
      return dir;
    } catch {}
    const parent = path.dirname(dir);
    if (parent === dir) return start;
    dir = parent;
  }
}

// --- Fetching ---
async function fetchItem(ref, base) {
  let url = ref;
  if (!isUrl(ref)) {
    const name = ref.replace(/^\//, "").replace(/\.json$/, "");
    url = `${base.replace(/\/$/, "")}/${name}.json`;
  }
  const res = await fetch(url);
  if (!res.ok) throw new Error(`Failed to fetch ${url}: ${res.status} ${res.statusText}`);
  const json = await res.json();
  if (!json?.name || !json?.type) throw new Error(`Invalid item at ${url} (missing name/type)`);
  return json;
}

async function resolveAll(entries, base, seen = new Map()) {
  const queue = [...entries];
  while (queue.length) {
    const ref = queue.shift();
    const key = isUrl(ref) ? ref : `${base}/${ref.replace(/^\//, "").replace(/\.json$/, "")}.json`;
    if (seen.has(key)) continue;
    const item = await fetchItem(ref, base);
    seen.set(key, item);
    (item.registryDependencies || []).forEach((d) => queue.push(d));
  }
  return Array.from(seen.values());
}

// --- Paths & writing ---
const PREFIX_STRIP = [
  "components/",
  "components\\",
  "components/ui/",
  "components/ui\\",
  "lib/",
  "lib\\",
  "hooks/",
  "hooks\\",
  "styles/",
  "styles\\",
  "app/",
  "app\\",
  "ui/",
  "ui\\",
];

function stripKnownPrefixes(p) {
  let s = p.replace(/^\/+/, "");
  for (const pre of PREFIX_STRIP) if (s.startsWith(pre)) return s.slice(pre.length);
  return s;
}

function fileTargetPath(file) {
  const { type, path: fpath, target } = file;
  if ((type === "registry:file" || type === "registry:page") && target) {
    return target.replace(/^\//, "");
  }
  const base = TYPE_TARGETS[type];
  if (!base) throw new Error(`No TYPE_TARGETS mapping for ${type}`);
  const rest = stripKnownPrefixes(fpath);
  return path.join(base, rest);
}

function rewriteCnImports(code) {
  // Replace exactly `import { cn } from "@/lib/utils"`
  return code.replace(
    /import\s+\{\s*cn\s*\}\s+from\s+["']@\/lib\/utils["'];?/g,
    "import { cn } from '@/lib/cn';",
  );
}

async function writeItemFiles(item, pkgDir, { dryRun }) {
  const written = [];
  for (const f of item.files || []) {
    const rel = fileTargetPath(f);
    const abs = path.join(pkgDir, rel);
    if (dryRun) {
      console.log(`[dry-run] ${item.name}: would write ${rel}`);
      continue;
    }
    await ensureDir(path.dirname(abs));
    const content = f.content ?? "";
    await fs.writeFile(abs, rewriteCnImports(content), "utf8");
    written.push(rel);
  }
  return written;
}

function installPeerDeps(pkgDir, deps, { dryRun }) {
  const list = dedupe(deps);
  if (!list.length) return;
  console.log(`\n[pnpm] adding as peerDependencies in ${pkgDir}:`, list.join(" "));
  if (dryRun) return;
  const res = spawnSync("pnpm", ["-C", pkgDir, "add", "--save-peer", ...list], {
    stdio: "inherit",
  });
  if (res.status !== 0) throw new Error("pnpm add --save-peer failed");
}

function installDevDeps(pkgDir, devDeps, { dryRun }) {
  const list = dedupe(devDeps);
  if (!list.length) return;
  console.log(`\n[pnpm] adding as devDependencies in ${pkgDir}:`, list.join(" "));
  if (dryRun) return;
  const res = spawnSync("pnpm", ["-C", pkgDir, "add", "-D", ...list], {
    stdio: "inherit",
  });
  if (res.status !== 0) throw new Error("pnpm add -D failed");
}

// --- Hints ---
function printTailwindHints(items) {
  for (const it of items) {
    if (it.tailwind?.config) {
      console.log(`\n[tailwind] ${it.name} -> merge into your Tailwind config:`);
      console.log(JSON.stringify(it.tailwind.config, null, 2));
    }
    if (it.cssVars) {
      console.log(`\n[cssVars] ${it.name} -> merge into your theme or CSS:`);
      console.log(JSON.stringify(it.cssVars, null, 2));
    }
    if (it.css) {
      console.log(
        `\n[css] ${it.name} -> add these blocks to packages/ui/tailwind.css (translate JSON to CSS):`,
      );
      console.log(JSON.stringify(it.css, null, 2));
    }
    if (it.envVars && Object.keys(it.envVars).length) {
      console.log(`\n[env] ${it.name} -> add to .env/.env.example:`);
      for (const [k, v] of Object.entries(it.envVars)) console.log(`  ${k}=${v}`);
    }
  }
}

function collectComponentDirs(writtenRelPaths) {
  const dirs = new Set();
  for (const rel of writtenRelPaths) {
    const norm = rel.replace(/\\/g, "/"); // windows safe
    // we only care about things under src/components or src/components/ui
    const m = norm.match(/^src\/components(?:\/ui)?\/[^\/]+/);
    if (m) dirs.add(m[0]);
  }
  return Array.from(dirs);
}

async function updateBarrelExports(pkgDir, componentDirs) {
  if (!componentDirs.length) return;

  const barrelPath = path.join(pkgDir, "src", "index.ts");

  // Build desired export lines from component dirs
  const desired = componentDirs.map((d) => {
    // Turn "src/components/button" -> "./components/button"
    const spec = "./" + d.replace(/^src\//, "").replace(".tsx", "");
    return `export * from '${spec}';`;
  });

  let existing = "";
  try {
    existing = await fs.readFile(barrelPath, "utf8");
  } catch {
    // create a new barrel
    const header = `// Auto-generated exports by shadcn-add${os.EOL}`;
    const body = Array.from(new Set(desired)).sort().join(os.EOL) + os.EOL;
    await fs.mkdir(path.dirname(barrelPath), { recursive: true });
    await fs.writeFile(barrelPath, header + body, "utf8");
    console.log(`[barrel] created src/index.ts with ${desired.length} export(s)`);
    return;
  }

  const lines = new Set(
    existing
      .split(/\r?\n/)
      .map((l) => l.trim())
      .filter(Boolean),
  );

  let added = 0;
  for (const exp of desired) {
    if (!lines.has(exp)) {
      lines.add(exp);
      added++;
    }
  }

  if (added > 0) {
    const next =
      Array.from(lines)
        .sort((a, b) => a.localeCompare(b))
        .join(os.EOL) + os.EOL;
    await fs.writeFile(barrelPath, next, "utf8");
    console.log(`[barrel] updated src/index.ts with ${added} new export(s)`);
  } else {
    console.log("[barrel] no new exports needed");
  }
}

// --- Main ---
(async function main() {
  assertPnpm();
  const args = parseArgs(process.argv);
  const root = await repoRoot();
  const sub = args._[0];
  const items = args._.slice(1);
  const pkgDir = path.resolve(root, args.pkg || DEFAULTS.pkg);
  const registry = String(args.registry || DEFAULTS.registry);
  const dryRun = Boolean(args["dry-run"]);

  if (sub !== "add" || items.length === 0) {
    console.log(
      "Usage: node tools/shadcn-add.mjs add <item...> [--pkg packages/ui] [--registry https://ui.shadcn.com] [--dry-run]",
    );
    process.exit(sub === "add" ? 0 : 1);
  }

  console.log(`[info] registry: ${registry}`);
  console.log(`[info] package: ${path.relative(root, pkgDir)}`);

  // Resolve all items + nested deps
  const resolved = await resolveAll(items, registry);

  // Write files & collect deps
  const allDeps = [];
  const allDev = [];
  const allWritten = [];
  for (const it of resolved) {
    const files = await writeItemFiles(it, pkgDir, { dryRun });
    console.log(`\n[wrote] ${it.name} (${it.type}) -> ${files.length} file(s)`);
    files.forEach((f) => console.log("  ", f));
    allWritten.push(...files);

    if (Array.isArray(it.dependencies)) allDeps.push(...it.dependencies);
    if (Array.isArray(it.devDependencies)) allDev.push(...it.devDependencies);
  }

  if (!dryRun) {
    const componentDirs = collectComponentDirs(allWritten);
    await updateBarrelExports(pkgDir, componentDirs);
  }

  // Install deps (peer) + dev deps
  installPeerDeps(pkgDir, allDeps, { dryRun });
  installDevDeps(pkgDir, allDev, { dryRun });

  // Print hints
  printTailwindHints(resolved);

  console.log("\nDone.");
})().catch((e) => {
  console.error(e);
  process.exit(1);
});

{"semantic": {"background": {"type": "color", "value": {"baseColor": "var(--color-neutral-0)"}}, "foreground": {"type": "color", "value": {"baseColor": "var(--color-neutral-950)", "alpha": "var(--opacity-overlay-87)"}}, "card": {"type": "color", "value": {"baseColor": "var(--color-neutral-0)"}}, "card-foreground": {"type": "color", "value": {"baseColor": "var(--foreground)"}}, "popover": {"type": "color", "value": {"baseColor": "var(--color-neutral-0)"}}, "popover-foreground": {"type": "color", "value": {"baseColor": "var(--foreground)"}}, "primary": {"type": "color", "value": {"baseColor": "var(--color-brand-sea)"}}, "primary-foreground": {"type": "color", "value": {"baseColor": "var(--color-brand-cream)"}}, "secondary": {"type": "color", "value": {"baseColor": "var(--color-neutral-50)"}}, "secondary-foreground": {"type": "color", "value": {"baseColor": "var(--color-neutral-950)", "alpha": "var(--opacity-overlay-87)"}}, "muted": {"type": "color", "value": {"baseColor": "var(--color-neutral-50)"}}, "muted-foreground": {"type": "color", "value": {"baseColor": "var(--color-neutral-950)", "alpha": "var(--opacity-overlay-60)"}}, "accent": {"type": "color", "value": {"baseColor": "var(--color-accent-saphire-400)", "alpha": "var(--opacity-overlay-33)"}}, "accent-foreground": {"type": "color", "value": {"baseColor": "var(--color-accent-saphire-700)"}}, "destructive": {"type": "color", "value": {"baseColor": "var(--color-brand-red)"}}, "destructive-foreground": {"type": "color", "value": {"baseColor": "var(--color-neutral-0)"}}, "border": {"type": "color", "value": {"baseColor": "var(--color-neutral-200)"}}, "input": {"type": "color", "value": {"baseColor": "var(--color-neutral-200)"}}, "ring": {"type": "color", "value": {"baseColor": "var(--color-accent-saphire-600)", "alpha": "var(--opacity-overlay-87)"}}, "sidebar": {"type": "color", "value": {"baseColor": "var(--background)"}}, "sidebar-foreground": {"type": "color", "value": {"baseColor": "var(--foreground)"}}, "sidebar-primary": {"type": "color", "value": {"baseColor": "var(--primary)"}}, "sidebar-primary-foreground": {"type": "color", "value": {"baseColor": "var(--primary-foreground)"}}, "sidebar-accent": {"type": "color", "value": {"baseColor": "var(--secondary)"}}, "sidebar-accent-foreground": {"type": "color", "value": {"baseColor": "var(--secondary-foreground)"}}, "sidebar-border": {"type": "color", "value": {"baseColor": "var(--border)"}}, "sidebar-ring": {"type": "color", "value": {"baseColor": "var(--ring)"}}}}
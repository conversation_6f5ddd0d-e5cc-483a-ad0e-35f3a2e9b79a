# RS Design System

A monorepo containing the **RS Design System** — shared UI components, design tokens, and tooling for building consistent user experiences across RS products.

---

## Documentation

- [**Working with Shadcn Components**](../../wiki/Shadcn-Component-Import-Tool-(shad:add))
  Guide for importing Shadcn registry components into `@rsds/ui`, installing peer dependencies, and updating exports.

- [**Evaluating Tokens + Figma Integration**](../../wiki/Integrating-@rsds-tokens-with-Figma)
  Proposal for syncing `@rsds/tokens` with Figma via Tokens Studio to create a tighter design–dev workflow.

---

## Packages

| Package              | Description                                     |
|---------------------|-----------------------------------------------|
| `@rsds/ui`          | Component library (React + Tailwind)          |
| `@rsds/tokens`      | Source of truth for design tokens (Style Dictionary) |
| `docs`              | Storybook and documentation site              |

---

## Getting Started

```bash
# Install dependencies
pnpm install

# Build everything
pnpm build

# Start Storybook (component previews)
pnpm storybook
```

---

## Contributing

1. Create a branch:
   ```bash
   git checkout -b feat/my-change
   ```
2. Make your changes (run `pnpm dev` to work in watch mode).
3. Run lint/typecheck/tests:
   ```bash
   pnpm lint && pnpm typecheck && pnpm test
   ```
4. Commit with a [Changeset](https://github.com/changesets/changesets):
   ```bash
   pnpm changeset
   ```
5. Open a PR for review.


import {
  Button,
  Checkbox,
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Switch,
  Textarea,
} from "@rsds/ui";
import type { <PERSON>a, StoryObj } from "@storybook/react-vite";
import * as React from "react";
import { type SubmitHandler, useForm } from "react-hook-form";

const meta: Meta = {
  title: "Forms/Form Primitives",
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
    controls: { disable: true },
    docs: {
      description: {
        component:
          "Examples showing how the Form primitives compose with react-hook-form. Each story demonstrates a canonical pattern (inputs, selects, validation, arrays, disabled state, etc.).",
      },
    },
  },
};
export default meta;

interface LoginValues {
  email: string;
  password: string;
  remember: boolean;
}

interface ProfileValues {
  name: string;
  bio: string;
  role: string;
  marketing: boolean;
  twoFA: boolean;
}

function StorySection({ title, children }: React.PropsWithChildren<{ title: string }>) {
  return (
    <div className="w-[360px] space-y-4">
      <h3 className="font-medium text-muted-foreground text-sm">{title}</h3>
      {children}
    </div>
  );
}

export const BasicLogin: StoryObj = {
  render: () => {
    const methods = useForm<LoginValues>({
      defaultValues: { email: "", password: "", remember: false },
      mode: "onSubmit",
    });

    const onSubmit: SubmitHandler<LoginValues> = (data) => {
      alert(JSON.stringify(data, null, 2));
    };

    return (
      <StorySection title="Basic login form">
        <Form {...methods}>
          <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={methods.control}
              name="email"
              rules={{
                required: "Email is required",
                pattern: {
                  value: /[^@\s]+@[^@\s]+\.[^@\s]+/,
                  message: "Enter a valid email",
                },
              }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input placeholder="<EMAIL>" type="email" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={methods.control}
              name="password"
              rules={{
                required: "Password is required",
                minLength: { value: 8, message: "Min 8 characters" },
              }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Password</FormLabel>
                  <FormControl>
                    <Input type="password" placeholder="••••••••" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={methods.control}
              name="remember"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-md border p-3">
                  <div className="space-y-0.5">
                    <FormLabel>Remember me</FormLabel>
                    <FormDescription>Stay signed in for 30 days</FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      aria-label="Remember me"
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <div className="flex gap-2">
              <Button type="submit" className="flex-1">
                Sign in
              </Button>
              <Button
                type="button"
                variant="outline"
                className="flex-1"
                onClick={() => methods.reset()}
              >
                Reset
              </Button>
            </div>
          </form>
        </Form>
      </StorySection>
    );
  },
};

export const MixedControls: StoryObj = {
  render: () => {
    const methods = useForm<ProfileValues>({
      defaultValues: {
        name: "",
        bio: "",
        role: "user",
        marketing: true,
        twoFA: false,
      },
    });

    const onSubmit: SubmitHandler<ProfileValues> = (data) => alert(JSON.stringify(data, null, 2));

    return (
      <StorySection title="Mixed controls">
        <Form {...methods}>
          <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={methods.control}
              name="name"
              rules={{ required: "Name is required" }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Jane Doe" {...field} />
                  </FormControl>
                  <FormDescription>This will be displayed on your profile.</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={methods.control}
              name="bio"
              rules={{
                maxLength: {
                  value: 160,
                  message: "Keep it under 160 characters",
                },
              }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Bio</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Tell us a little about yourself" {...field} />
                  </FormControl>
                  <FormDescription>Short and sweet is best.</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={methods.control}
              name="role"
              rules={{ required: "Choose a role" }}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Role</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a role" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="admin">Admin</SelectItem>
                      <SelectItem value="editor">Editor</SelectItem>
                      <SelectItem value="user">User</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>Your permissions depend on this selection.</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={methods.control}
              name="marketing"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start gap-3 rounded-md border p-3">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      aria-label="Marketing"
                    />
                  </FormControl>
                  <div className="space-y-0.5">
                    <FormLabel>Marketing emails</FormLabel>
                    <FormDescription>Get product updates and tips.</FormDescription>
                  </div>
                </FormItem>
              )}
            />

            <FormField
              control={methods.control}
              name="twoFA"
              render={({ field }) => (
                <FormItem className="flex items-center justify-between rounded-md border p-3">
                  <div className="space-y-0.5">
                    <FormLabel>Two-factor Authentication</FormLabel>
                    <FormDescription>Extra security for your account.</FormDescription>
                  </div>
                  <FormControl>
                    <Switch checked={field.value} onCheckedChange={field.onChange} />
                  </FormControl>
                </FormItem>
              )}
            />

            <div className="flex gap-2">
              <Button type="submit" className="flex-1">
                Save
              </Button>
              <Button
                type="button"
                variant="outline"
                className="flex-1"
                onClick={() => methods.reset()}
              >
                Reset
              </Button>
            </div>
          </form>
        </Form>
      </StorySection>
    );
  },
};

export const DisabledState: StoryObj = {
  render: () => {
    const [disabled, setDisabled] = React.useState(true);
    const methods = useForm<LoginValues>({
      defaultValues: {
        email: "<EMAIL>",
        password: "password",
        remember: false,
      },
    });

    return (
      <StorySection title="Disabled form state">
        <div className="mb-2 flex items-center gap-2">
          <Label htmlFor="toggle-disabled" className="text-sm">
            Disabled
          </Label>
          <Switch id={React.useId()} checked={disabled} onCheckedChange={setDisabled} />
        </div>
        <Form {...methods}>
          <form className="space-y-4" onSubmit={methods.handleSubmit(() => {})}>
            <fieldset disabled={disabled} className={disabled ? "opacity-60" : undefined}>
              <FormField
                control={methods.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input type="email" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={methods.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <Input type="password" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </fieldset>
            <Button type="submit">Submit</Button>
          </form>
        </Form>
      </StorySection>
    );
  },
};

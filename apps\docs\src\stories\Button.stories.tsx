import { Button, buttonVariantsConfig } from "@rsds/ui";
import type * as reactVite from "@storybook/react-vite";
import { Plus } from "lucide-react";

const meta = {
  title: "Components/Button",
  component: Button,
  tags: ["autodocs"],
  argTypes: {
    disabled: {
      control: "boolean",
    },
    variant: {
      control: "select",
      options: Object.keys(buttonVariantsConfig.variant),
    },
    size: {
      control: "select",
      options: Object.keys(buttonVariantsConfig.size),
    },
    rounded: {
      control: "select",
      options: Object.keys(buttonVariantsConfig.rounded),
    },
  },
  args: {
    size: "default",
    variant: "default",
  },
} satisfies reactVite.Meta<typeof Button>;

export default meta;

type Story = reactVite.StoryObj<typeof Button>;

export const Default: Story = {
  args: {
    children: "Default",
    variant: "default",
  },
};

export const Destructive: Story = {
  args: {
    children: "Delete",
    variant: "destructive",
  },
};

export const Outline: Story = {
  args: {
    children: "Outline",
    variant: "outline",
  },
};

export const Secondary: Story = {
  args: {
    children: "Secondary",
    variant: "secondary",
  },
};

export const Ghost: Story = {
  args: {
    children: "Ghost",
    variant: "ghost",
  },
};

export const Link: Story = {
  args: {
    children: "Link",
    variant: "link",
  },
};

export const Icon: Story = {
  args: {
    size: "icon",
    children: <Plus className="h-4 w-4" />,
  },
};

import StyleDictionary from "style-dictionary";
import tinycolor from "tinycolor2";

// Make a namespaced CSS var name:
//  - semantic/*  -> strip the leading "semantic-" (shadcn keys)
//  - others      -> prefix with the top-level group ("spacing", "z", "opacity", "color", "radius", "shadow", "font", ...)
function toCssVarName(token) {
  const [group] = token.path;

  // Join full path with hyphens: color-neutral-scale-50, spacing-10, etc.
  let name = token.path.join("-");

  // Strip trailing -default: radius-default -> radius
  name = name.replace(/-default$/i, "");

  if (group === "semantic") {
    // For semantic tokens we keep shadcn-compatible names: --primary, --muted-foreground, ...
    // (Your semantic JSON should already be using those keys under "semantic".)
    name = name.replace(/^semantic-/, "");
    return `--${name}`;
  }

  // For primitives we keep the full namespaced path to avoid collisions:
  // e.g. --spacing-10, --z-10, --opacity-overlay-12, --color-neutral-scale-50
  return `--${name}`;
}

function isCssVar(v) {
  return typeof v === "string" && /\bvar\(.+\)/.test(v);
}

function toHslChannels(input) {
  const c = tinycolor(input);
  if (!c.isValid()) return null;
  const { h, s, l } = c.toHsl();
  return `${Math.round(h)} ${Math.round(s * 100)}% ${Math.round(l * 100)}%`;
}

StyleDictionary.registerFormat({
  name: "css-variables-theme-color4",
  format: ({ dictionary, options }) => {
    const selector = options?.selector ?? ":root";

    const lines = dictionary.allTokens.map((t) => {
      const cssName = toCssVarName(t);
      const val = t.value;

      // 1) semantic color tokens: { baseColor, alpha? }
      const isSemantic = t.path[0] === "semantic";
      if (
        isSemantic &&
        val &&
        typeof val === "object" &&
        !Array.isArray(val) &&
        "baseColor" in val
      ) {
        const base = val.baseColor;
        const alpha = "alpha" in val ? val.alpha : undefined;

        // If baseColor is already a CSS var, compose with it directly.
        if (isCssVar(base)) {
          return `  ${cssName}: hsl(${base} / ${alpha ?? "1"});`;
        }
        // Else try to parse and emit H S% L% channels.
        const channels = toHslChannels(base);
        if (channels) return `  ${cssName}: hsl(${channels} / ${alpha ?? "1"});`;

        // Last resort
        return `  ${cssName}: ${base};`;
      }

      // 2) opacity primitives: emit raw numbers
      if (t.type === "opacity" || /(^|[.-])opacity([.-]|$)/i.test(t.path.join("."))) {
        return `  ${cssName}: ${val};`;
      }

      // 3) color primitives: emit HSL channels "H S% L%"
      const channels = toHslChannels(val);
      if (channels) return `  ${cssName}: ${channels};`;

      // 4) non-color primitives (spacing, radius, shadow, z, font, etc.): pass through
      return `  ${cssName}: ${val};`;
    });

    return `${selector} {\n${lines.join("\n")}\n}\n`;
  },
});

import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Textarea,
} from "@rsds/ui";
import type { Meta, StoryObj } from "@storybook/react-vite";
import { type SubmitHandler, useForm } from "react-hook-form";

const meta: Meta<typeof Card> = {
  title: "Components/Card",
  component: Card,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
  },
};
export default meta;

type Story = StoryObj<typeof Card>;

export const Basic: Story = {
  render: () => (
    <Card className="w-96">
      <CardHeader>
        <CardTitle>Basic Card</CardTitle>
        <CardDescription>Simple structure with default spacing.</CardDescription>
      </CardHeader>
      <CardContent>
        <p className="text-muted-foreground text-sm">
          This is the content area of the card. Place text, inputs, or other UI elements here.
        </p>
      </CardContent>
      <CardFooter>
        <Button>Action</Button>
      </CardFooter>
    </Card>
  ),
};

export const RichContent: Story = {
  render: () => (
    <Card className="w-[480px]">
      <CardHeader>
        <CardTitle>Documentation Card</CardTitle>
        <CardDescription>Good for long-form notes or details.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque imperdiet.</p>
        <p>
          Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque
          laudantium.
        </p>
        <div className="bg-rsds-plum-600">hello hsl( var(--color-1177-plum-600))</div>
      </CardContent>
      <CardFooter>
        <Button variant="outline">Secondary</Button>
        <Button className="ml-auto">Primary</Button>
      </CardFooter>
    </Card>
  ),
};

interface SubscribeValues {
  email: string;
  message: string;
}

export const WithForm: Story = {
  render: () => {
    const methods = useForm<SubscribeValues>({
      defaultValues: { email: "", message: "" },
    });

    const onSubmit: SubmitHandler<SubscribeValues> = (data) => {
      alert(JSON.stringify(data, null, 2));
    };

    return (
      <Card className="w-[400px]">
        <CardHeader>
          <CardTitle>Subscribe</CardTitle>
          <CardDescription>Stay updated with our newsletter.</CardDescription>
        </CardHeader>
        <Form {...methods}>
          <form onSubmit={methods.handleSubmit(onSubmit)}>
            <CardContent className="grid gap-4">
              <FormField
                control={methods.control}
                name="email"
                rules={{ required: "Email is required" }}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input placeholder="<EMAIL>" type="email" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={methods.control}
                name="message"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Message</FormLabel>
                    <FormControl>
                      <Textarea placeholder="Message (optional)" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>
            <CardFooter className="justify-end">
              <Button type="submit">Subscribe</Button>
            </CardFooter>
          </form>
        </Form>
      </Card>
    );
  },
};

export const NoFooter: Story = {
  render: () => (
    <Card className="w-80">
      <CardHeader>
        <CardTitle>Display Card</CardTitle>
        <CardDescription>Used for showing information only.</CardDescription>
      </CardHeader>
      <CardContent>
        <p>
          This card has no footer, making it suitable for static content such as status messages or
          read-only data.
        </p>
      </CardContent>
    </Card>
  ),
};

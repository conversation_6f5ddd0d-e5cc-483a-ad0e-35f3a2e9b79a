import StyleDictionary from "style-dictionary";

// Register custom formatter in ESM
StyleDictionary.registerFormat({
  name: "tokens-studio-json",
  format: ({ dictionary }) => {
    // Group by tokenSet (use "global" as default if you don't have sets)
    const sets = { global: {} };

    dictionary.allTokens.forEach((t) => {
      // Build a dotted path, e.g. color.bg.default
      const path = t.path.join(".");

      // Tokens Studio likes { "value": "...", "type": "color" | "dimension" | "font" | ... }
      const entry = { value: t.value };
      if (t.attributes?.category) entry.type = t.attributes.category;

      // Create nested object structure under sets.global
      const parts = path.split(".");
      let cursor = sets.global;
      while (parts.length > 1) {
        const p = parts.shift();
        cursor[p] = cursor[p] || {};
        cursor = cursor[p];
      }
      cursor[parts[0]] = entry;
    });

    // Optional: define themes (aka variable modes in Figma)
    const $themes = [
      // Example:
      // {
      //   name: 'Light',
      //   selectedTokenSets: { global: 'source' },
      //   $figmaStyleReferences: {}
      // }
    ];

    const $metadata = { tokenSetOrder: Object.keys(sets) };

    return JSON.stringify({ ...sets, $themes, $metadata }, null, 2);
  },
});

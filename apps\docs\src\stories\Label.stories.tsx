import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react-vite";

import { Label } from "@rsds/ui";
import { useId } from "react";

const meta: Meta<typeof Label> = {
  title: "Components/Label",
  component: Label,
  tags: ["autodocs"],
  args: {
    children: "Email",
    htmlFor: "email",
  },
  argTypes: {
    children: {
      control: "text",
      description: "Label text",
    },
    htmlFor: {
      control: "text",
      description: "ID of the associated field",
    },
    className: {
      control: "text",
      description: "Additional classes to style the label",
    },
  },
};

export default meta;

type Story = StoryObj<typeof Label>;

export const Playground: Story = {
  render: (args) => (
    <div className="space-y-2">
      <Label {...args} />
    </div>
  ),
};

export const WithInput: Story = {
  render: (args) => (
    <div className="grid w-80 gap-2">
      <Label {...args} />
      <input
        id={args.htmlFor}
        className="peer h-9 w-full rounded-md border border-input bg-background px-3 text-sm outline-none ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
        placeholder="<EMAIL>"
        type="email"
      />
    </div>
  ),
};

export const DisabledPeer: Story = {
  args: {
    children: "Disabled field",
    htmlFor: "disabled-email",
  },
  render: (args) => (
    <div className="grid w-80 gap-2">
      <Label {...args} />
      <input
        id={args.htmlFor}
        className="peer h-9 w-full rounded-md border border-input bg-muted px-3 text-muted-foreground text-sm"
        placeholder="can't type here"
        type="email"
        disabled
      />
      <p className="text-muted-foreground text-xs">
        The label inherits <code>peer-disabled:cursor-not-allowed</code> and{" "}
        <code>peer-disabled:opacity-70</code>.
      </p>
    </div>
  ),
};

export const Required: Story = {
  args: {
    children: (
      <span>
        Password{" "}
        <span aria-hidden className="text-red-600">
          *
        </span>
      </span>
    ),
  },
  render: (args) => {
    const id = useId();
    const hintId = useId();
    return (
      <div className="grid w-80 gap-2">
        <Label htmlFor={id} {...args} />
        {/** biome-ignore lint/a11y/useAriaPropsSupportedByRole: we got a label */}
        <input
          id={id}
          className="peer h-9 w-full rounded-md border border-input bg-background px-3 text-sm"
          type="password"
          aria-required
          aria-describedby={hintId}
        />
        <p id={hintId} className="text-muted-foreground text-xs">
          Must be at least 8 characters.
        </p>
      </div>
    );
  },
};

export const ToneAndSizeTweaks: Story = {
  render: (args) => {
    const subtleId = useId();
    const loudId = useId();

    return (
      <div className="grid w-80 gap-4">
        <div className="grid gap-2">
          <Label {...args} htmlFor={subtleId} className="text-muted-foreground text-xs">
            Subtle label
          </Label>
          <input id={subtleId} className="peer h-8 rounded-md border px-2 text-sm" />
        </div>
        <div className="grid gap-2">
          <Label htmlFor={loudId} className="text-base tracking-tight">
            Prominent label
          </Label>
          <input id={loudId} className="peer h-10 rounded-md border px-3 text-base" />
        </div>
      </div>
    );
  },
};

{"$schema": "./node_modules/@biomejs/biome/configuration_schema.json", "formatter": {"indentStyle": "space", "indentWidth": 2, "lineWidth": 100}, "linter": {"enabled": true, "rules": {"recommended": true, "suspicious": {"noUnknownAtRules": "off"}, "nursery": {"useSortedClasses": {"level": "error", "options": {"attributes": ["classList"], "functions": ["clsx", "cva", "tw", "tw.*"]}}}}, "includes": ["**", "!.turbo", "!dist/**", "!build/**"]}, "files": {"includes": ["**"]}}
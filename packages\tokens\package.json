{"name": "@rsds/tokens", "version": "0.1.0", "type": "module", "private": false, "main": "dist/index.css", "exports": {".": {"import": "./dist/index.css"}, "./css": "./dist/index.css"}, "files": ["dist", "src/tailwind.preset.ts"], "scripts": {"build": "style-dictionary build --config ./style-dictionary.config.mjs", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "nodemon --watch tokens --exec \"style-dictionary build --config ./style-dictionary.config.mjs\"", "lint": "biome lint .", "lint:fix": "biome lint --write .", "format": "biome format --write ."}, "devDependencies": {"deepmerge": "^4.3.1", "fast-glob": "^3.3.3", "nodemon": "^3.1.10", "rimraf": "^6.0.1", "style-dictionary": "^5.0.2", "tinycolor2": "^1.6.0"}}
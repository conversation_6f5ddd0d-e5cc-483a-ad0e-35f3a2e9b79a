import fs from "node:fs";
import path from "node:path";
import { fileURLToPath } from "node:url";
import deepmerge from "deepmerge";
import glob from "fast-glob";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function tagAllLeaves(obj, tag) {
  // Walk the token object tree; add attributes.theme='dark' to each leaf token
  if (obj && typeof obj === "object") {
    const keys = Object.keys(obj);
    const isLeaf = keys.includes("value"); // SD token leaf heuristic
    if (isLeaf) {
      obj.attributes = { ...(obj.attributes || {}), theme: tag };
      return obj;
    }
    for (const k of keys) tagAllLeaves(obj[k], tag);
  }
  return obj;
}

export async function mergeDarkOverrides(dictionary /*, platformConfig */) {
  const tokensDir = path.resolve(__dirname, "..", "tokens");
  const files = await glob("semantic/**/dark.*.json", { cwd: tokensDir });

  let dark = {};
  for (const rel of files) {
    const abs = path.join(tokensDir, rel);
    const json = JSON.parse(fs.readFileSync(abs, "utf8"));
    tagAllLeaves(json, "dark");
    dark = deepmerge(dark, json);
  }
  // Merge base dict with tagged dark overrides (dark wins on conflicts)
  return deepmerge(dictionary, dark);
}

{"name": "docs", "private": true, "version": "0.0.0", "type": "module", "scripts": {"lint": "biome lint .", "lint:fix": "biome lint --write .", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "format": "biome format --write ."}, "dependencies": {"@rsds/tokens": "workspace:*", "@rsds/ui": "workspace:*", "lucide-react": "^0.541.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0"}, "devDependencies": {"@fontsource/public-sans": "^5.2.7", "@chromatic-com/storybook": "^4.1.1", "@eslint/js": "^9.33.0", "@storybook/addon-a11y": "^9.1.5", "@storybook/addon-docs": "^9.1.5", "@storybook/addon-onboarding": "^9.1.5", "@storybook/addon-styling-webpack": "^2.0.0", "@storybook/addon-themes": "^9.1.5", "@storybook/addon-vitest": "^9.1.5", "@storybook/react-vite": "^9.1.5", "@tailwindcss/vite": "^4.1.13", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^5.0.0", "@vitest/browser": "^3.2.4", "@vitest/coverage-v8": "^3.2.4", "autoprefixer": "^10.4.21", "globals": "^16.3.0", "playwright": "^1.55.0", "postcss": "^8.5.6", "storybook": "^9.1.5", "tailwindcss": "4.1.12", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2", "vitest": "^3.2.4"}}
# -------------------------------
# Node / pnpm
# -------------------------------
node_modules/
.pnpm-store/
.pnpm-debug.log*
.npmrc
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
yarn.lock

# Keep pnpm lockfile, ignore others
!pnpm-lock.yaml

# -------------------------------
# Build outputs
# -------------------------------
dist/
build/
out/
.next/
.nuxt/
.storybook-out/
storybook-static/

# -------------------------------
# Caches
# -------------------------------
.cache/
.vite/
.turbo/
.eslintcache
.stylelintcache
.cache-loader/
coverage/

# -------------------------------
# Environment files
# -------------------------------
.env
.env.local
.env.*.local

# -------------------------------
# IDE / Editor cruft
# -------------------------------
.vscode/
.idea/
*.swp
*.swo

# -------------------------------
# OS files
# -------------------------------
.DS_Store
Thumbs.db

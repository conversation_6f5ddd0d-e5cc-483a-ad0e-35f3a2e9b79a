import { Input, Label } from "@rsds/ui";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";
import { Eye, EyeOff, Mail } from "lucide-react";
import * as React from "react";

const meta: Meta<typeof Input> = {
  title: "Components/Input",
  component: Input,
  tags: ["autodocs"],
  args: {
    placeholder: "Type here...",
    type: "text",
  },
  argTypes: {
    type: {
      control: {
        type: "select",
        options: [
          "text",
          "email",
          "password",
          "search",
          "number",
          "url",
          "tel",
          "date",
          "time",
          "file",
        ],
      },
      description: "Native input type",
    },
    placeholder: { control: "text" },
    disabled: { control: "boolean" },
    className: { control: "text" },
  },
};

export default meta;
type Story = StoryObj<typeof Input>;

export const Playground: Story = {
  render: (args) => (
    <div className="w-80">
      <Input {...args} />
    </div>
  ),
};

export const WithLabel: Story = {
  render: () => {
    const id = React.useId();
    return (
      <div className="grid w-80 gap-2">
        <Label htmlFor={id}>Email</Label>
        <Input id={id} type="email" placeholder="<EMAIL>" />
      </div>
    );
  },
};

export const Disabled: Story = {
  render: () => {
    const id = React.useId();
    return (
      <div className="grid w-80 gap-2">
        <Label htmlFor={id}>Disabled</Label>
        <Input id={id} disabled placeholder="Unavailable" />
      </div>
    );
  },
};

export const TypesShowcase: Story = {
  render: () => {
    const textId = React.useId();
    const emailId = React.useId();
    const passwordId = React.useId();
    const numberId = React.useId();
    const searchId = React.useId();
    return (
      <div className="grid w-96 gap-4">
        <div className="grid gap-2">
          <Label htmlFor={textId}>Text</Label>
          <Input id={textId} type="text" placeholder="Jane Doe" />
        </div>
        <div className="grid gap-2">
          <Label htmlFor={emailId}>Email</Label>
          <Input id={emailId} type="email" placeholder="<EMAIL>" />
        </div>
        <div className="grid gap-2">
          <Label htmlFor={passwordId}>Password</Label>
          <Input id={passwordId} type="password" placeholder="••••••••" />
        </div>
        <div className="grid gap-2">
          <Label htmlFor={numberId}>Number</Label>
          <Input id={numberId} type="number" placeholder="42" />
        </div>
        <div className="grid gap-2">
          <Label htmlFor={searchId}>Search</Label>
          <Input id={searchId} type="search" placeholder="Search…" />
        </div>
      </div>
    );
  },
};

export const WithIcon: Story = {
  render: () => (
    <div className="w-80">
      <div className="relative">
        <span className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
          <Mail className="h-4 w-4 text-muted-foreground" />
        </span>
        <Input type="email" placeholder="<EMAIL>" className="pl-9" />
      </div>
    </div>
  ),
};

export const WithPrefixSuffix: Story = {
  render: () => {
    const amountId = React.useId();
    const urlId = React.useId();
    return (
      <div className="grid w-96 gap-4">
        <div className="grid gap-1.5">
          <Label htmlFor={amountId}>Amount</Label>
          <div className="flex items-center rounded-md border border-input shadow-sm focus-within:ring-1 focus-within:ring-ring">
            <span className="select-none px-3 text-muted-foreground">$</span>
            <Input
              id={amountId}
              type="number"
              className="border-0 shadow-none focus-visible:ring-0"
              placeholder="0.00"
            />
            <span className="select-none px-3 text-muted-foreground">USD</span>
          </div>
        </div>
        <div className="grid gap-1.5">
          <Label htmlFor={urlId}>Website</Label>
          <div className="flex items-center rounded-md border border-input shadow-sm focus-within:ring-1 focus-within:ring-ring">
            <span className="select-none px-3 text-muted-foreground">https://</span>
            <Input
              id={urlId}
              type="url"
              className="border-0 shadow-none focus-visible:ring-0"
              placeholder="example.com"
            />
          </div>
        </div>
      </div>
    );
  },
};

export const InvalidState: Story = {
  render: () => {
    const id = React.useId();
    return (
      <div className="grid w-80 gap-1.5">
        <Label htmlFor={id}>Username</Label>
        <Input
          id={id}
          aria-invalid
          className="border-destructive focus-visible:ring-destructive"
          placeholder="only letters, numbers, dashes"
        />
        <p className="text-destructive text-xs">Username is taken.</p>
      </div>
    );
  },
};

export const File: Story = {
  render: () => {
    const id = React.useId();
    return (
      <div className="grid w-96 gap-2">
        <Label htmlFor={id}>Resume</Label>
        <Input id={id} type="file" />
        <p className="text-muted-foreground text-xs">PDF or DOCX up to 10MB.</p>
      </div>
    );
  },
};

export const PasswordWithToggle: Story = {
  render: function PasswordWithToggleRender() {
    const id = React.useId();
    const [visible, setVisible] = React.useState(false);
    return (
      <div className="grid w-80 gap-2">
        <Label htmlFor={id}>Password</Label>
        <div className="relative">
          <Input
            id={id}
            type={visible ? "text" : "password"}
            placeholder="••••••••"
            className="pr-9"
          />
          <button
            type="button"
            aria-label={visible ? "Hide password" : "Show password"}
            onClick={() => setVisible((v) => !v)}
            className="absolute inset-y-0 right-0 flex items-center pr-3 text-muted-foreground hover:opacity-80"
          >
            {visible ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          </button>
        </div>
      </div>
    );
  },
};

export const ControlledWithCounter: Story = {
  render: function ControlledRender() {
    const id = React.useId();
    const [value, setValue] = React.useState("");
    const max = 30;
    return (
      <div className="grid w-96 gap-1.5">
        <Label htmlFor={id}>Short bio</Label>
        <Input
          id={id}
          value={value}
          onChange={(e) => setValue(e.target.value)}
          placeholder="Tell us something fun"
          className={
            value.length > max ? "border-destructive focus-visible:ring-destructive" : undefined
          }
          aria-describedby={`${id}-hint`}
        />
        <div
          className="flex items-center justify-between text-muted-foreground text-xs"
          id={`${id}-hint`}
        >
          <span>Max {max} characters</span>
          <span>
            {value.length}/{max}
          </span>
        </div>
      </div>
    );
  },
};

export const SizeTweaks: Story = {
  render: () => {
    const sm = React.useId();
    const md = React.useId();
    const lg = React.useId();
    return (
      <div className="grid w-96 gap-4">
        <div className="grid gap-1.5">
          <Label htmlFor={sm}>Small</Label>
          <Input id={sm} className="h-8 text-xs" placeholder="h-8 text-xs" />
        </div>
        <div className="grid gap-1.5">
          <Label htmlFor={md}>Default</Label>
          <Input id={md} placeholder="h-9 md:text-sm (default)" />
        </div>
        <div className="grid gap-1.5">
          <Label htmlFor={lg}>Large</Label>
          <Input id={lg} className="h-10 text-base" placeholder="h-10 text-base" />
        </div>
      </div>
    );
  },
};

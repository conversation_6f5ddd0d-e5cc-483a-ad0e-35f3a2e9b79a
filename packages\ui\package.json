{"name": "@rsds/ui", "version": "0.1.0", "private": false, "type": "module", "sideEffects": false, "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./css": "./tailwind.css"}, "files": ["tailwind.css"], "scripts": {"build": "tsup", "dev": "tsup --watch", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "biome lint .", "lint:fix": "biome lint --write .", "format": "biome format --write ."}, "peerDependencies": {"@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@rsds/tokens": "workspace:*", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "react": "^18 || ^19", "react-dom": "^18 || ^19", "react-hook-form": "^7.62.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@rsds/tokens": "workspace:*", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cpy-cli": "^6.0.0", "lucide-react": "^0.541.0", "react": "^18 || ^19", "react-dom": "^18 || ^19", "react-hook-form": "^7.62.0", "rimraf": "^6.0.1", "tailwind-merge": "^3.3.1", "tsup": "^8.5.0", "typescript": "5.9.2"}}
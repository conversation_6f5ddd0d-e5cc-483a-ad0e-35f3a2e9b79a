import { Meta, ColorPalette, ColorItem } from '@storybook/addon-docs/blocks';




<Meta title="Colors" />

<ColorPalette>
  <ColorItem
    title="1177 Sky"
    subtitle="Sky color scale"
    colors={{
      'Sky 50': 'hsl(240, 14%, 95%)',
      'Sky 200': 'hsl(210, 33%, 83%)',
      'Sky 400': 'hsl(199, 100%, 45%)',
      'Sky 600': 'hsl(214, 44%, 40%)',
      'Sky 900': 'hsl(230, 26%, 32%)',
    }}
  />
  <ColorItem
    title="1177 Grass"
    subtitle="Grass color scale"
    colors={{
      'Grass 50': 'hsl(120, 36%, 95%)',
      'Grass 200': 'hsl(94, 32%, 81%)',
      'Grass 400': 'hsl(100, 63%, 45%)',
      'Grass 600': 'hsl(107, 55%, 41%)',
      'Grass 900': 'hsl(97, 34%, 27%)',
    }}
  />
  <ColorItem
    title="1177 Plum"
    subtitle="Plum color scale"
    colors={{
      'Plum 50': 'hsl(300, 25%, 95%)',
      'Plum 200': 'hsl(335, 64%, 87%)',
      'Plum 400': 'hsl(340, 83%, 70%)',
      'Plum 600': 'hsl(317, 44%, 46%)',
      'Plum 900': 'hsl(314, 43%, 25%)',
    }}
  />
  <ColorItem
    title="1177 Sun"
    subtitle="Sun color scale"
    colors={{
      'Sun 50': 'hsl(48, 100%, 94%)',
      'Sun 200': 'hsl(47, 100%, 80%)',
      'Sun 400': 'hsl(46, 100%, 50%)',
      'Sun 600': 'hsl(28, 100%, 49%)',
      'Sun 900': 'hsl(15, 81%, 25%)',
    }}
  />
  <ColorItem
    title="1177 Stone"
    subtitle="Stone color scale"
    colors={{
      'Stone 50': 'hsl(180, 3%, 95%)',
      'Stone 200': 'hsl(210, 5%, 86%)',
      'Stone 400': 'hsl(210, 1%, 51%)',
      'Stone 600': 'hsl(210, 1%, 39%)',
      'Stone 900': 'hsl(0, 0%, 21%)',
    }}
  />
  <ColorItem
    title="Brand Colors"
    subtitle="Primary brand palette"
    colors={{
      'Brand Cream': 'hsl(52, 87%, 94%)',
      'Brand Sea': 'hsl(191, 50%, 37%)',
      'Brand Yellow': 'hsl(49, 98%, 59%)',
      'Brand Red': 'hsl(348, 99%, 45%)',
      'Brand Earth': 'hsl(40, 27%, 29%)',
    }}
  />
  <ColorItem
    title="Accent Colors"
    subtitle="Accent color palette"
    colors={{
      'Accent Red 500': 'hsl(347, 100%, 55%)',
      'Accent Sea 100': 'hsl(180, 100%, 91%)',
      'Accent Sea 200': 'hsl(188, 100%, 83%)',
      'Accent Sea 300': 'hsl(190, 68%, 71%)',
      'Accent Sea 800': 'hsl(191, 49%, 26%)',
      'Accent Sea 900': 'hsl(191, 94%, 14%)',
    }}
  />
  <ColorItem
    title="Accent Sapphire"
    subtitle="Sapphire accent colors"
    colors={{
      'Sapphire 400': 'hsl(212, 99%, 69%)',
      'Sapphire 600': 'hsl(212, 99%, 46%)',
      'Sapphire 700': 'hsl(210, 99%, 34%)',
    }}
  />
  <ColorItem
    title="Neutral Scale"
    subtitle="Grayscale palette"
    colors={{
      'Neutral 0': 'hsl(0, 0%, 100%)',
      'Neutral 50': 'hsl(0, 0%, 94%)',
      'Neutral 100': 'hsl(0, 0%, 88%)',
      'Neutral 150': 'hsl(0, 0%, 82%)',
      'Neutral 200': 'hsl(0, 0%, 75%)',
      'Neutral 300': 'hsl(0, 0%, 63%)',
      'Neutral 400': 'hsl(0, 0%, 50%)',
      'Neutral 500': 'hsl(0, 0%, 44%)',
      'Neutral 600': 'hsl(0, 0%, 38%)',
      'Neutral 650': 'hsl(0, 0%, 31%)',
      'Neutral 700': 'hsl(0, 0%, 25%)',
      'Neutral 800': 'hsl(0, 0%, 13%)',
      'Neutral 950': 'hsl(0, 0%, 0%)',
    }}
  />
  <ColorItem
    title="Meta Colors"
    subtitle="Special purpose colors"
    colors={{
      'Meta Undefined': 'hsl(324, 100%, 50%)',
    }}
  />
</ColorPalette>

import { withThemeByDataAttribute } from "@storybook/addon-themes";
import type * as reactVite from "@storybook/react-vite";
import "@fontsource/public-sans";
import "../src/styles.css";

const preview: reactVite.Preview = {
  parameters: {
    backgrounds: { disable: true },
    controls: {
      expanded: true,
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },

    layout: "centered",

    a11y: {
      // 'todo' - show a11y violations in the test UI only
      // 'error' - fail CI on a11y violations
      // 'off' - skip a11y checks entirely
      test: "todo",
    },
  },
  decorators: [
    withThemeByDataAttribute({
      defaultTheme: "light",
      themes: { dark: "dark", light: "light" },
      attributeName: "data-theme", // This sets [data-theme="dark"] or [data-theme="light"]
    }),
  ],
};

export default preview;

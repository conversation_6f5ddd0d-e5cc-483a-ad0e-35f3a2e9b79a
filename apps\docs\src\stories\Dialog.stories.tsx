import {
  But<PERSON>,
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Des<PERSON>,
  Di<PERSON>Footer,
  Di<PERSON><PERSON>eader,
  Di<PERSON>Title,
  DialogTrigger,
} from "@rsds/ui";
import type { Meta, StoryObj } from "@storybook/react-vite";

const meta: Meta = {
  title: "Components/Dialog",
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
  },
};

export default meta;

type Story = StoryObj;

export const Basic: Story = {
  render: () => (
    <Dialog>
      <DialogTrigger asChild>
        <Button>Open Dialog</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Basic Dialog</DialogTitle>
          <DialogDescription>Just a simple dialog with some text.</DialogDescription>
        </DialogHeader>
        <p className="text-muted-foreground text-sm">Here is some content inside the dialog.</p>
        <DialogFooter>
          <Button variant="outline">Cancel</Button>
          <Button>Confirm</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  ),
};

export const WithLongContent: Story = {
  render: () => (
    <Dialog>
      <DialogTrigger asChild>
        <Button>Open Long Dialog</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Long Content</DialogTitle>
          <DialogDescription>Scroll inside the dialog.</DialogDescription>
        </DialogHeader>
        <div className="max-h-64 space-y-4 overflow-y-auto pr-2">
          {Array.from({ length: 20 }, (_, i) => (
            // biome-ignore lint/suspicious/noArrayIndexKey: no need to check this here
            <p key={i}>
              This is paragraph {i + 1}. Lorem ipsum dolor sit amet, consectetur adipiscing elit.
            </p>
          ))}
        </div>
        <DialogFooter>
          <Button>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  ),
};

export const WithForm: Story = {
  render: () => (
    <Dialog>
      <DialogTrigger asChild>
        <Button>Subscribe</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Subscribe to newsletter</DialogTitle>
          <DialogDescription>Enter your email to get updates.</DialogDescription>
        </DialogHeader>
        <form className="grid gap-4 py-2">
          <input
            type="email"
            placeholder="<EMAIL>"
            className="h-9 rounded-md border px-3 text-sm"
          />
          <DialogFooter>
            <Button type="submit">Submit</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  ),
};

export const WithoutFooter: Story = {
  render: () => (
    <Dialog>
      <DialogTrigger asChild>
        <Button>Open Dialog</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Dialog without footer</DialogTitle>
          <DialogDescription>Sometimes you just need static info.</DialogDescription>
        </DialogHeader>
        <p>Here is some text without actions at the bottom.</p>
      </DialogContent>
    </Dialog>
  ),
};

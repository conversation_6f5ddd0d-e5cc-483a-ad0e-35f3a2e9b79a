import StyleDictionary from "style-dictionary";

/**
 * Name transform: path → dotted
 * Provide BOTH keys for x-version safety (v4 uses `transform`, v5 uses `transformer`).
 */
StyleDictionary.registerTransform({
  name: "name/pathToKebab",
  type: "name",
  transform: (token) => token.path.join("."), // v4
  transformer: (token) => token.path.join("."), // v5
});

/**
 * File header: prints a short banner atop each generated file.
 * v5 expects `registerFileHeader`.
 */
StyleDictionary.registerFileHeader({
  name: "orgHeader",
  fileHeader: () => ["AUTO-GENERATED — do not edit.", "Source: @rsds/tokens JSON files."],
});
//
// Make a unique, stable name from the full path.
// - semantic/*    -> strip leading "semantic-" (becomes shadcn: --primary, --border, ...)
// - everything    -> keep namespaced path (color-neutral-scale-50, spacing-10, z-10, radius-sm, shadow-md, font-size-sm, ...)
// - strip "-default" suffix so "radius-default" => "radius"
StyleDictionary.registerTransform({
  name: "name/rsds",
  type: "name",
  transform: (token) => {
    // token.path is an array of keys from the root
    const parts = token.path.slice();

    // 1) Remove leading "semantic" segment from the path so we don't prefix semantic tokens
    if (parts[0] === "semantic") parts.shift();

    // 2) Join with hyphens
    let name = parts.join("-");

    // 3) Strip trailing "-default" (case-insensitive)
    name = name.replace(/-default$/i, "");

    return name;
  },
});

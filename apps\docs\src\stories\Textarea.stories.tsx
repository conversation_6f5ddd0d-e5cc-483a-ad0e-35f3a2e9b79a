import { Label, Textarea } from "@rsds/ui";
import type { Meta, StoryObj } from "@storybook/react-vite";
import * as React from "react";

const meta: Meta<typeof Textarea> = {
  title: "Components/Textarea",
  component: Textarea,
  tags: ["autodocs"],
  args: {
    placeholder: "Type your message...",
    disabled: false,
  },
  argTypes: {
    disabled: { control: "boolean" },
    placeholder: { control: "text" },
    className: { control: "text" },
  },
};

export default meta;

type Story = StoryObj<typeof Textarea>;

export const Basic: Story = {
  render: (args) => {
    const id = React.useId();
    return (
      <div className="grid w-96 gap-2">
        <Label htmlFor={id}>Message</Label>
        <Textarea id={id} {...args} />
      </div>
    );
  },
};

export const Disabled: Story = {
  args: { disabled: true, placeholder: "Disabled textarea" },
  render: (args) => {
    const id = React.useId();
    return (
      <div className="grid w-96 gap-2">
        <Label htmlFor={id}>Disabled</Label>
        <Textarea id={id} {...args} />
      </div>
    );
  },
};

export const WithDescription: Story = {
  render: (args) => {
    const id = React.useId();
    const hintId = `${id}-hint`;
    return (
      <div className="grid w-96 gap-1.5">
        <Label htmlFor={id}>Comment</Label>
        <Textarea id={id} aria-describedby={hintId} placeholder="Leave a comment..." {...args} />
        <p id={hintId} className="text-muted-foreground text-xs">
          Markdown is supported.
        </p>
      </div>
    );
  },
};

export const Controlled: Story = {
  render: () => {
    const id = React.useId();
    const [value, setValue] = React.useState("");
    return (
      <div className="grid w-96 gap-2">
        <Label htmlFor={id}>Controlled</Label>
        <Textarea
          id={id}
          value={value}
          onChange={(e) => setValue(e.target.value)}
          placeholder="Type here..."
        />
        <p className="text-muted-foreground text-xs">{value.length} characters</p>
      </div>
    );
  },
};

export const SizeTweaks: Story = {
  render: () => {
    const smallId = React.useId();
    const defaultId = React.useId();
    const largeId = React.useId();
    return (
      <div className="grid w-96 gap-4">
        <div className="grid gap-1.5">
          <Label htmlFor={smallId}>Small</Label>
          <Textarea id={smallId} className="h-16 text-xs" placeholder="Small textarea" />
        </div>
        <div className="grid gap-1.5">
          <Label htmlFor={defaultId}>Default</Label>
          <Textarea id={defaultId} placeholder="Default size" />
        </div>
        <div className="grid gap-1.5">
          <Label htmlFor={largeId}>Large</Label>
          <Textarea id={largeId} className="h-32 text-base" placeholder="Large textarea" />
        </div>
      </div>
    );
  },
};

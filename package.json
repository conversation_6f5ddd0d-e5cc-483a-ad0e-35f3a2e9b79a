{"name": "rsds", "private": true, "packageManager": "pnpm@10.15.0", "engines": {"node": ">=22.15.0"}, "scripts": {"build": "turbo run build", "dev": "turbo run dev --parallel", "lint": "turbo run lint", "lint:fix": "turbo run lint:fix", "format": "turbo run format", "test": "turbo run test", "typecheck": "turbo run typecheck", "clean": "turbo run clean && rimraf node_modules", "release": "changeset version && pnpm -w i && turbo run build && changeset publish", "changeset": "changeset", "ci": "turbo run lint typecheck test build --cache-dir=.turbo", "shad:add": "node tools/shadcn-add.mjs add", "storybook": "turbo run dev --filter=@rsds/ui... & pnpm --filter docs run storybook", "prepare": "husky"}, "devDependencies": {"@biomejs/biome": "2.2.4", "@changesets/cli": "^2.29.6", "@types/node": "^24.3.0", "husky": "9.1.7", "lint-staged": "^16.1.5", "rimraf": "^6.0.1", "turbo": "^2.5.6", "typescript": "5.9.2"}, "pnpm": {"overrides": {"tailwindcss": "4.1.12", "typescript": "5.9.2", "react": "19.1.1", "react-dom": "19.1.1"}, "peerDependencyRules": {"allowedVersions": {"tailwindcss": "4.1.12", "typescript": "5.9.2"}}}, "lint-staged": {"*.{js,jsx,ts,tsx,json,css}": ["biome lint --write", "biome format --write"]}}
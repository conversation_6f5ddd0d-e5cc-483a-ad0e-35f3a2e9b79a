# @rsds/ui

Shared component library for RS Design System. Built on top of [shadcn/ui](https://ui.shadcn.com) with the same API, styled with Tailwind, and published as a consumable package.

---

## Installation

Install the package and required peer dependencies:

```bash
pnpm add @rsds/ui @fontsource/public-sans \
  @radix-ui/react-checkbox @radix-ui/react-dialog @radix-ui/react-label \
  @radix-ui/react-radio-group @radix-ui/react-select @radix-ui/react-slot \
  @radix-ui/react-switch class-variance-authority clsx react-hook-form tailwind-merge
```

> React and React DOM must already be installed in your project (18 or 19 supported).

Import the font once in your app entry file (e.g., `src/main.tsx`, `_app.tsx`, or root layout):

```ts
import '@fontsource/public-sans';
```

---

## Tailwind Setup

Include the design system’s base styles in your global stylesheet so they are built together with your app:

```css
/* app/globals.css or your main Tailwind entry */
@import "tailwindcss";
@import "@rsds/ui/css";

@source '../node_modules/@rsds/ui';
```

---

## Usage Example

### Button

```tsx
import { Button } from '@rsds/ui';

export default function Example() {
  return (
    <div className="space-x-4">
      <Button>Default</Button>
      <Button variant="secondary">Secondary</Button>
      <Button variant="destructive">Delete</Button>
    </div>
  );
}
```


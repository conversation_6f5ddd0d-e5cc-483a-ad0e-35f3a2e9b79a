# @rsds/tokens

Design token source for RS Design System. Built with [Style Dictionary](https://amzn.github.io/style-dictionary/) and distributed as CSS variables,

---

## Installation

```bash
pnpm add @rsds/tokens
```

Then import the CSS once in your global stylesheet:

```css
/* app/globals.css */
@import "@rsds/tokens/css";
```

This makes all design tokens available as CSS custom properties (variables).

---

## Development

### Build tokens
```bash
pnpm --filter @rsds/tokens build
```
Generates `dist/index.css` and other outputs.

### Watch during development
```bash
pnpm --filter @rsds/tokens dev
```
Rebuilds automatically when token files change.


import path from "node:path";
import { fileURLToPath } from "node:url";
import StyleDictionary from "style-dictionary";

import "./generators/register-core.mjs";
import "./generators/css-variable-theme-color4.mjs";
import { mergeDarkOverrides } from "./generators/preprocessor-merge-dark.mjs";
import "./generators/token-studio.mjs";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const R = (...p) => path.resolve(__dirname, "tokens", ...p).replace(/\\/g, '/');
const DIST = path.resolve(__dirname, "dist");

StyleDictionary.registerFormat({
  name: "css/imports",
  format: () => ['@import "./core.css";', '@import "./dark.css";', ""].join("\n"),
});

StyleDictionary.registerPreprocessor({
  name: "merge-dark-overrides",
  preprocessor: mergeDarkOverrides,
});

StyleDictionary.registerFilter({
  name: "only/dark/semantics",
  filter: (token) => token.attributes?.theme === "dark",
});

export default {
  source: [R("primitives/**/*.json"), R("semantic/**/light.*.json")],

  platforms: {
    core: {
      buildPath: `${DIST}/`,
      transforms: ["name/rsds"],
      files: [
        {
          destination: "core.css",
          format: "css-variables-theme-color4",
          options: { selector: ":root", mode: "hsl", fileHeader: "orgHeader" },
        },
        {
          destination: "tokens.raw.json",
          format: "json/nested",
          options: { fileHeader: "orgHeader" },
        },
        {
          destination: "index.css",
          format: "css/imports",
          options: { fileHeader: "orgHeader" },
        },
      ],
    },

    dark: {
      buildPath: `${DIST}/`,
      transforms: ["name/rsds"],
      preprocessors: ["merge-dark-overrides"],
      files: [
        {
          destination: "dark.css",
          format: "css-variables-theme-color4",
          options: {
            selector: '[data-theme="dark"]',
            mode: "hsl",
            fileHeader: "orgHeader",
          },
          filter: "only/dark/semantics",
        },
      ],
    },
    tokensStudio: {
      buildPath: `${DIST}/`,
      transformGroup: "web",
      files: [
        {
          destination: "tokens.tok.json",
          format: "tokens-studio-json",
        },
      ],
    },
  },
};


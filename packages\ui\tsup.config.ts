import fs from "node:fs";
import path from "node:path";
import { defineConfig } from "tsup";
import pkg from "./package.json";

// Auto-discover component entry points (so imports keep working as you add more)
function componentEntries() {
  const root = path.join(__dirname, "src", "components");
  if (!fs.existsSync(root)) return {};
  const entries: Record<string, string> = {};
  for (const dir of fs.readdirSync(root)) {
    const idx = path.join(root, dir, "index.ts");
    if (fs.existsSync(idx)) {
      entries[`components/${dir}/index`] = idx;
    }
  }
  return entries;
}

export default defineConfig({
  entry: {
    index: "src/index.ts",
    ...componentEntries(),
  },
  format: ["esm", "cjs"],
  dts: true,
  sourcemap: true,
  clean: true,
  external: Object.keys(pkg.peerDependencies),
});

import { Label, Switch } from "@rsds/ui";
import type { <PERSON>a, StoryObj } from "@storybook/react-vite";
import { useId, useState } from "react";

const meta: Meta<typeof Switch> = {
  title: "Components/Switch",
  component: Switch,
  tags: ["autodocs"],
  args: {
    disabled: false,
  },
  argTypes: {
    disabled: { control: "boolean" },
    className: { control: "text" },
  },
};

export default meta;

type Story = StoryObj<typeof Switch>;

export const Basic: Story = {
  render: (args) => {
    const id = useId();
    return (
      <div className="flex items-center space-x-2">
        <Switch id={id} {...args} />
        <Label htmlFor={id}>Enable notifications</Label>
      </div>
    );
  },
};

export const Disabled: Story = {
  args: { disabled: true },
  render: (args) => {
    const id = useId();
    return (
      <div className="flex items-center space-x-2">
        <Switch id={id} {...args} />
        <Label htmlFor={id}>Disabled switch</Label>
      </div>
    );
  },
};

export const Controlled: Story = {
  render: () => {
    const id = useId();
    const [enabled, setEnabled] = useState(false);
    return (
      <div className="flex items-center space-x-2">
        <Switch id={id} checked={enabled} onCheckedChange={setEnabled} />
        <Label htmlFor={id}>Controlled: {String(enabled)}</Label>
      </div>
    );
  },
};

export const WithDescription: Story = {
  render: () => {
    const id = useId();
    const hintId = `${id}-hint`;
    return (
      <div className="flex flex-col space-y-1.5">
        <div className="flex items-center space-x-2">
          <Switch id={id} aria-describedby={hintId} />
          <Label htmlFor={id}>Dark mode</Label>
        </div>
        <p id={hintId} className="text-muted-foreground text-xs">
          Toggle between light and dark themes.
        </p>
      </div>
    );
  },
};

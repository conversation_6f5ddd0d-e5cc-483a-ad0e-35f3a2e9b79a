import { Checkbox, Label } from "@rsds/ui";
import type { <PERSON><PERSON>, StoryObj } from "@storybook/react-vite";
import * as React from "react";

type CheckedState = boolean | "indeterminate";

const meta: Meta<typeof Checkbox> = {
  title: "Components/Checkbox",
  component: Checkbox,
  tags: ["autodocs"],
  args: {
    disabled: false,
  },
  argTypes: {
    disabled: { control: "boolean" },
    className: { control: "text" },
  },
};

export default meta;

type Story = StoryObj<typeof Checkbox>;

export const Basic: Story = {
  render: (args) => (
    <div className="flex items-center space-x-2">
      <Checkbox id={React.useId()} {...args} />
      <Label htmlFor="c1">Accept terms and conditions</Label>
    </div>
  ),
};

export const Disabled: Story = {
  args: { disabled: true },
  render: (args) => (
    <div className="flex items-center space-x-2">
      <Checkbox id={React.useId()} {...args} />
      <Label htmlFor="c2">Disabled checkbox</Label>
    </div>
  ),
};

export const Controlled: Story = {
  render: () => {
    const [checked, setChecked] = React.useState<CheckedState>(false);
    return (
      <div className="flex items-center space-x-2">
        <Checkbox id={React.useId()} checked={checked} onCheckedChange={setChecked} />
        <Label htmlFor="c3">Controlled: {String(checked)}</Label>
      </div>
    );
  },
};

export const WithLongLabel: Story = {
  render: () => (
    <div className="flex max-w-sm items-start space-x-2">
      <Checkbox id={React.useId()} />
      <Label htmlFor="c4">
        I agree to the terms and conditions, including the privacy policy and data usage guidelines.
      </Label>
    </div>
  ),
};
